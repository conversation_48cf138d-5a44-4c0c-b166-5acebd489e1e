#!/bin/bash

# deploy_loancity
# Script to deploy the loan-city WordPress theme to the remote server
# Usage: ./deploy_loancity [subfolder_or_file]
#
# Parameters:
#   subfolder_or_file - Optional. Specific subfolder or file within the loan-city theme to deploy.
#                       If not provided, the entire theme will be deployed.

# Base paths
LOCAL_THEME_PATH="/Users/<USER>/PhpstormProjects/loan-city/wp-content/themes/loan-city"
REMOTE_THEME_PATH="/var/www/lc-landing-page/wp-content/themes/loan-city"

# Check if a specific subfolder or file is provided
if [ $# -eq 0 ]; then
    # No arguments provided, sync the entire theme directory
    SOURCE_PATH="${LOCAL_THEME_PATH}/"
    DEST_PATH="${REMOTE_THEME_PATH}/"
    echo "Deploying entire loan-city theme..."
else
    # Argument provided, check if it exists
    SUBFOLDER_OR_FILE="$1"
    SOURCE_PATH="${LOCAL_THEME_PATH}/${SUBFOLDER_OR_FILE}"

    # Check if the specified path exists
    if [ ! -e "$SOURCE_PATH" ]; then
        echo "Error: '$SUBFOLDER_OR_FILE' does not exist in the loan-city theme directory."
        echo "Please provide a valid subfolder or file path relative to the theme directory."
        exit 1
    fi

    # Determine if it's a directory (needs trailing slash for rsync)
    if [ -d "$SOURCE_PATH" ]; then
        SOURCE_PATH="${SOURCE_PATH}/"
        DEST_PATH="${REMOTE_THEME_PATH}/${SUBFOLDER_OR_FILE}/"
    else
        # It's a file
        DEST_PATH="${REMOTE_THEME_PATH}/${SUBFOLDER_OR_FILE}"
    fi

    echo "Deploying '$SUBFOLDER_OR_FILE' from loan-city theme..."
fi

# Sync the specified path to the remote server
rsync -avuzh -e "ssh -i ~/.ssh/id_ed25519_loancity" --rsync-path "sudo rsync" "$SOURCE_PATH" "ubuntu@18.141.33.96:$DEST_PATH"

echo "Deployment completed successfully!"
