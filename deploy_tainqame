#!/bin/bash

# deploy_tainqame
# Script to deploy the personal homepage to the remote server
# Usage: ./deploy_tainqame [subfolder_or_file]
#
# Parameters:
#   subfolder_or_file - Optional. Specific subfolder or file within the personal homepage to deploy.
#                       If not provided, the entire site will be deployed.

# Base paths
LOCAL_PATH="/Users/<USER>/Sites/localhost/personal/personal-homepage/dist"
REMOTE_PATH="/home/<USER>/domains/tainqa.me/public_html"

# Check if a specific subfolder or file is provided
if [ $# -eq 0 ]; then
    # No arguments provided, sync the entire directory
    SOURCE_PATH="${LOCAL_PATH}/"
    DEST_PATH="${REMOTE_PATH}/"
    echo "Deploying entire personal homepage..."
else
    # Argument provided, check if it exists
    SUBFOLDER_OR_FILE="$1"
    SOURCE_PATH="${LOCAL_PATH}/${SUBFOLDER_OR_FILE}"

    # Check if the specified path exists
    if [ ! -e "$SOURCE_PATH" ]; then
        echo "Error: '$SUBFOLDER_OR_FILE' does not exist in the personal homepage directory."
        echo "Please provide a valid subfolder or file path relative to the site directory."
        exit 1
    fi

    # Determine if it's a directory (needs trailing slash for rsync)
    if [ -d "$SOURCE_PATH" ]; then
        SOURCE_PATH="${SOURCE_PATH}/"
        DEST_PATH="${REMOTE_PATH}/${SUBFOLDER_OR_FILE}/"
    else
        # It's a file
        DEST_PATH="${REMOTE_PATH}/${SUBFOLDER_OR_FILE}"
    fi

    echo "Deploying '$SUBFOLDER_OR_FILE' from personal homepage..."
fi

# Sync the specified path to the remote server
rsync -avuzh -e "ssh -p 65002 -i ~/.ssh/id_ed25519_tainqame" "$SOURCE_PATH" "u710167401@151.106.117.41:$DEST_PATH"

echo "Deployment completed successfully!"